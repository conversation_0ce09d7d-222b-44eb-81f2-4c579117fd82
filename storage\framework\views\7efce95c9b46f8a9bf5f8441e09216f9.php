

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/toastr.css')); ?>">
    <style>
        .nav-tabs .nav-link.has-errors {
            border-color: #dc3545;
            color: #dc3545;
        }
        .nav-tabs .nav-link.has-errors:hover {
            border-color: #dc3545;
            color: #dc3545;
        }
        .tab-error-indicator {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .validation-summary {
            border-left: 4px solid #dc3545;
        }
        .validation-summary .alert-heading {
            color: #721c24;
        }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
<!-- // Basic multiple Column Form section start -->
<form method="POST" action="<?php echo e(route('admin.providers.store')); ?>" class="store form-horizontal" enctype="multipart/form-data">
<section id="multiple-column-form">
    <div class="row">
        <div class="col-md-3">
            <div class="col-12 card card-body">
                <div class="imgMontg col-12 text-center">
                    <div class="dropBox">
                        <div class="textCenter">
                            <div class="imagesUploadBlock">
                                <label class="uploadImg">
                                    <span><i class="feather icon-image"></i></span>
                                    <input type="file" accept="image/*" name="image" class="imageUploader">
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="text-center mt-2"><?php echo e(__('admin.profile_image')); ?></p>
            </div>
        </div>

        <div class="col-9">
            <div class="card">
                <div class="card-content">
                    <div class="card-body">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="type" value="provider">

                        <?php
                            // Define validation errors for each tab
                            $basicInfoFields = ['name', 'phone', 'country_code', 'email', 'password', 'password_confirmation', 'city_id', 'region_id', 'gender', 'image'];
                            $providerInfoFields = ['commercial_name.ar', 'commercial_name.en', 'commercial_register_no', 'institution_name', 'sponsor_name', 'sponsor_phone', 'nationality', 'residence_type', 'salon_type', 'description', 'comission'];
                            $documentsFields = ['logo', 'commercial_register_image', 'residence_image', 'id_number', 'id_image'];
                            $salonImagesFields = ['salon_images'];

                            // Check if each tab has errors
                            $hasBasicInfoErrors = false;
                            $hasProviderInfoErrors = false;
                            $hasDocumentsErrors = false;
                            $hasSalonImagesErrors = false;
                            $hasWorkingHoursErrors = false;

                            $basicInfoErrorCount = 0;
                            $providerInfoErrorCount = 0;
                            $documentsErrorCount = 0;
                            $salonImagesErrorCount = 0;

                            foreach($basicInfoFields as $field) {
                                if($errors->has($field)) {
                                    $hasBasicInfoErrors = true;
                                    $basicInfoErrorCount++;
                                }
                            }

                            foreach($providerInfoFields as $field) {
                                if($errors->has($field)) {
                                    $hasProviderInfoErrors = true;
                                    $providerInfoErrorCount++;
                                }
                            }

                            foreach($documentsFields as $field) {
                                if($errors->has($field)) {
                                    $hasDocumentsErrors = true;
                                    $documentsErrorCount++;
                                }
                            }

                            foreach($salonImagesFields as $field) {
                                if($errors->has($field)) {
                                    $hasSalonImagesErrors = true;
                                    $salonImagesErrorCount++;
                                }
                            }

                            // Check working hours errors
                            if($errors->has('working_hours') || $errors->has('working_hours.*')) {
                                $hasWorkingHoursErrors = true;
                            }
                            foreach($errors->keys() as $key) {
                                if(str_starts_with($key, 'working_hours.')) {
                                    $hasWorkingHoursErrors = true;
                                    break;
                                }
                            }
                        ?>

                        <!-- Validation Errors Summary -->
                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger alert-dismissible mb-3" role="alert">
                                <h6 class="alert-heading">
                                    <i class="feather icon-alert-circle"></i> <?php echo e(__('admin.validation_errors_found')); ?>

                                </h6>
                                <p class="mb-2"><?php echo e(__('admin.please_check_the_following_tabs_for_errors')); ?>:</p>
                                <ul class="mb-0">
                                    <?php if($hasBasicInfoErrors): ?>
                                        <li><strong><?php echo e(__('admin.basic_information')); ?></strong> - <?php echo e($basicInfoErrorCount); ?> <?php echo e(__('admin.errors')); ?></li>
                                    <?php endif; ?>
                                    <?php if($hasProviderInfoErrors): ?>
                                        <li><strong><?php echo e(__('admin.provider_information')); ?></strong> - <?php echo e($providerInfoErrorCount); ?> <?php echo e(__('admin.errors')); ?></li>
                                    <?php endif; ?>
                                    <?php if($hasDocumentsErrors): ?>
                                        <li><strong><?php echo e(__('admin.documents')); ?></strong> - <?php echo e($documentsErrorCount); ?> <?php echo e(__('admin.errors')); ?></li>
                                    <?php endif; ?>
                                    <?php if($hasSalonImagesErrors): ?>
                                        <li><strong><?php echo e(__('admin.past_work')); ?></strong> - <?php echo e($salonImagesErrorCount); ?> <?php echo e(__('admin.errors')); ?></li>
                                    <?php endif; ?>
                                    <?php if($hasWorkingHoursErrors): ?>
                                        <li><strong><?php echo e(__('admin.working_hours')); ?></strong> - <?php echo e(__('admin.errors_found')); ?></li>
                                    <?php endif; ?>
                                </ul>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        <?php endif; ?>

                        <!-- Nav tabs -->
                        <ul class="nav nav-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active <?php echo e($hasBasicInfoErrors ? 'has-errors' : ''); ?>" id="basic-info-tab" data-toggle="tab" href="#basic-info" aria-controls="basic-info" role="tab" aria-selected="true">
                                    <i class="feather icon-user"></i> <?php echo e(__('admin.basic_information')); ?>

                                    <?php if($hasBasicInfoErrors): ?>
                                        <span class="badge badge-danger ml-1 tab-error-indicator"><?php echo e($basicInfoErrorCount); ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e($hasProviderInfoErrors ? 'has-errors' : ''); ?>" id="provider-info-tab" data-toggle="tab" href="#provider-info" aria-controls="provider-info" role="tab" aria-selected="false">
                                    <i class="feather icon-briefcase"></i> <?php echo e(__('admin.provider_information')); ?>

                                    <?php if($hasProviderInfoErrors): ?>
                                        <span class="badge badge-danger ml-1 tab-error-indicator"><?php echo e($providerInfoErrorCount); ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e($hasDocumentsErrors ? 'has-errors' : ''); ?>" id="documents-tab" data-toggle="tab" href="#documents" aria-controls="documents" role="tab" aria-selected="false">
                                    <i class="feather icon-file-text"></i> <?php echo e(__('admin.documents')); ?>

                                    <?php if($hasDocumentsErrors): ?>
                                        <span class="badge badge-danger ml-1 tab-error-indicator"><?php echo e($documentsErrorCount); ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link <?php echo e($hasSalonImagesErrors ? 'has-errors' : ''); ?>" id="salon-images-tab" data-toggle="tab" href="#salon-images" aria-controls="salon-images" role="tab" aria-selected="false">
                                    <i class="feather icon-image"></i> <?php echo e(__('admin.past_work')); ?>

                                    <?php if($hasSalonImagesErrors): ?>
                                        <span class="badge badge-danger ml-1 tab-error-indicator"><?php echo e($salonImagesErrorCount); ?></span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo e($hasWorkingHoursErrors ? 'has-errors' : ''); ?>" id="working-hours-tab" data-toggle="tab" href="#working-hours" aria-controls="working-hours" role="tab" aria-selected="false">
                                    <i class="feather icon-clock"></i> <?php echo e(__('admin.working_hours')); ?>

                                    <?php if($hasWorkingHoursErrors): ?>
                                        <span class="badge badge-danger ml-1 tab-error-indicator">!</span>
                                    <?php endif; ?>
                                </a>
                            </li>
                        </ul>

                        <!-- Tab panes -->
                        <div class="tab-content">
                            <!-- Basic Information Tab -->
                            <div class="tab-pane active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                                <div class="form-body mt-3">
                                    <div class="row">
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="name"><?php echo e(__('admin.name')); ?></label>
                                                <div class="controls">
                                                    <input type="text" name="name" value="<?php echo e(old('name')); ?>" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.write_the_name')); ?>" >
                                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="phone"><?php echo e(__('admin.phone_number')); ?></label>
                                                <div class="row">
                                                    <div class="col-md-4 col-12">
                                                        <select name="country_code" class="form-control select2 <?php $__errorArgs = ['country_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                            <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <option value="<?php echo e($country->key); ?>"
                                                                    <?php if(old('country_code', $settings['default_country']) == $country->id): ?>
                                                                        selected
                                                                    <?php endif; ?> >
                                                                <?php echo e('+'.$country->key); ?></option>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </select>
                                                        <?php $__errorArgs = ['country_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </div>
                                                    <div class="col-md-8 col-12">
                                                        <div class="controls">
                                                            <input type="number" name="phone" value="<?php echo e(old('phone')); ?>" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.enter_phone_number')); ?>" >
                                                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="email"><?php echo e(__('admin.email')); ?></label>
                                                <div class="controls">
                                                    <input type="email" name="email" value="<?php echo e(old('email')); ?>" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.enter_the_email')); ?>" >
                                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="password"><?php echo e(__('admin.password')); ?></label>
                                                <div class="controls">
                                                    <input type="password" name="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" >
                                                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="password_confirmation"><?php echo e(__('admin.password_confirmation')); ?></label>
                                                <div class="controls">
                                                    <input type="password" name="password_confirmation" class="form-control <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" >
                                                    <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                                <label for="region_id"><?php echo e(__('admin.Region')); ?></label>
                                                <div class="controls">
                                                    <select name="region_id" id="region_id" class="form-control select2">
                                                        <option value=""><?php echo e(__('admin.select_region')); ?></option>
                                                        <?php $__currentLoopData = $regions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($region->id); ?>"><?php echo e($region->name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
        
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="city_id"><?php echo e(__('admin.City')); ?></label>
                                                <div class="controls">
                                                    <select name="city_id" id="city_id" class="form-control select2">
                                                        <option value=""><?php echo e(__('admin.select_city')); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="gender"><?php echo e(__('admin.gender')); ?></label>
                                                <div class="controls">
                                                    <select name="gender" class="select2 form-control <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" >
                                                        <option value=""><?php echo e(__('admin.select_gender')); ?></option>
                                                        <option value="male" <?php echo e(old('gender') == 'male' ? 'selected' : ''); ?>><?php echo e(__('admin.male')); ?></option>
                                                        <option value="female" <?php echo e(old('gender') == 'female' ? 'selected' : ''); ?>><?php echo e(__('admin.female')); ?></option>
                                                    </select>
                                                    <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Provider Information Tab -->
                            <div class="tab-pane" id="provider-info" role="tabpanel" aria-labelledby="provider-info-tab">
                                <div class="form-body mt-3">
                                    <div class="row">
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="commercial_name_ar"><?php echo e(__('admin.commercial_name')); ?> (<?php echo e(__('admin.arabic')); ?>)</label>
                                                <div class="controls">
                                                    <input type="text" name="commercial_name[ar]" value="<?php echo e(old('commercial_name.ar')); ?>" class="form-control <?php $__errorArgs = ['commercial_name.ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.commercial_name')); ?> <?php echo e(__('admin.in_arabic')); ?>" >
                                                    <?php $__errorArgs = ['commercial_name.ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="commercial_name_en"><?php echo e(__('admin.commercial_name')); ?> (<?php echo e(__('admin.english')); ?>)</label>
                                                <div class="controls">
                                                    <input type="text" name="commercial_name[en]" value="<?php echo e(old('commercial_name.en')); ?>" class="form-control <?php $__errorArgs = ['commercial_name.en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.commercial_name')); ?> <?php echo e(__('admin.in_english')); ?>" >
                                                    <?php $__errorArgs = ['commercial_name.en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12 non-freelancer-fields">
                                            <div class="form-group">
                                                <label for="commercial_register_no"><?php echo e(__('admin.commercial_register_no')); ?></label>
                                                <div class="controls">
                                                    <input type="text" name="commercial_register_no" value="<?php echo e(old('commercial_register_no')); ?>" class="form-control <?php $__errorArgs = ['commercial_register_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.commercial_register_no')); ?>" >
                                                    <?php $__errorArgs = ['commercial_register_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="institution_name"><?php echo e(__('admin.institution_name')); ?></label>
                                                <div class="controls">
                                                    <input type="text" name="institution_name" value="<?php echo e(old('institution_name')); ?>" class="form-control <?php $__errorArgs = ['institution_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.institution_name')); ?>" >
                                                    <?php $__errorArgs = ['institution_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="sponsor_name"><?php echo e(__('admin.sponsor_name')); ?></label>
                                                <div class="controls">
                                                    <input type="text" name="sponsor_name" value="<?php echo e(old('sponsor_name')); ?>" class="form-control <?php $__errorArgs = ['sponsor_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.sponsor_name')); ?>" >
                                                    <?php $__errorArgs = ['sponsor_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="sponsor_phone"><?php echo e(__('admin.sponsor_phone')); ?></label>
                                                <div class="controls">
                                                    <input type="text" name="sponsor_phone" value="<?php echo e(old('sponsor_phone')); ?>" class="form-control <?php $__errorArgs = ['sponsor_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.sponsor_phone')); ?>" >
                                                    <?php $__errorArgs = ['sponsor_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="nationality"><?php echo e(__('admin.nationality')); ?></label>
                                                <div class="controls">
                                                    <select name="nationality" class="select2 form-control <?php $__errorArgs = ['nationality'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" >
                                                        <option value=""><?php echo e(__('admin.select_nationality')); ?></option>
                                                        <option value="saudi" <?php echo e(old('nationality') == 'saudi' ? 'selected' : ''); ?>><?php echo e(__('admin.saudi')); ?></option>
                                                        <option value="other" <?php echo e(old('nationality') == 'other' ? 'selected' : ''); ?>><?php echo e(__('admin.other')); ?></option>
                                                    </select>
                                                    <?php $__errorArgs = ['nationality'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- ID Number for Saudi Freelancers -->
                                        <div class="col-md-6 col-12 freelancer-fields" style="display: none;">
                                            <div class="form-group">
                                                <label for="id_number"><?php echo e(__('admin.id_number')); ?> <span class="text-danger freelancer-required">*</span></label>
                                                <div class="controls">
                                                    <input type="text" name="id_number" value="<?php echo e(old('id_number')); ?>" class="form-control <?php $__errorArgs = ['id_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.id_number')); ?>" maxlength="10">
                                                    <?php $__errorArgs = ['id_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="residence_type"><?php echo e(__('admin.residence_type')); ?></label>
                                                <div class="controls">
                                                    <select name="residence_type" class="select2 form-control <?php $__errorArgs = ['residence_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                        <option value=""><?php echo e(__('admin.select_residence_type')); ?></option>
                                                        <option value="individual" <?php echo e(old('residence_type') == 'individual' ? 'selected' : ''); ?>><?php echo e(__('admin.individual')); ?></option>
                                                        <option value="professional" <?php echo e(old('residence_type') == 'professional' ? 'selected' : ''); ?>><?php echo e(__('admin.professional')); ?></option>
                                                    </select>
                                                    <?php $__errorArgs = ['residence_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="salon_type"><?php echo e(__('admin.salon_type')); ?></label>
                                                <div class="controls">
                                                    <select name="salon_type" id="salon_type" class="select2 form-control <?php $__errorArgs = ['salon_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" >
                                                        <option value=""><?php echo e(__('admin.select_salon_type')); ?></option>
                                                        <option value="salon" <?php echo e(old('salon_type') == 'salon' ? 'selected' : ''); ?>><?php echo e(__('admin.salon')); ?></option>
                                                        <option value="beauty_center" <?php echo e(old('salon_type') == 'beauty_center' ? 'selected' : ''); ?>><?php echo e(__('admin.beauty_center')); ?></option>
                                                        <option value="freelancer" <?php echo e(old('salon_type') == 'freelancer' ? 'selected' : ''); ?>><?php echo e(__('admin.freelancer')); ?></option>
                                                    </select>
                                                    <?php $__errorArgs = ['salon_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12 col-12">
                                            <div class="form-group">
                                                <label for="description"><?php echo e(__('admin.description')); ?></label>
                                                <div class="controls">
                                                    <textarea name="description" class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3" placeholder="<?php echo e(__('admin.description')); ?>"><?php echo e(old('description')); ?></textarea>
                                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>


                                        <div class="col-md-4 col-12">
                                            <div class="form-group">
                                                <label><?php echo e(__('admin.in_home')); ?></label>
                                                <div class="controls">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" name="in_home" value="1" class="custom-control-input" id="in_home">
                                                        <label class="custom-control-label" for="in_home"><?php echo e(__('admin.provides_home_service')); ?></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-12">
                                            <div class="form-group">
                                                <label><?php echo e(__('admin.in_salon')); ?></label>
                                                <div class="controls">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" name="in_salon" value="1" class="custom-control-input" id="in_salon">
                                                        <label class="custom-control-label" for="in_salon"><?php echo e(__('admin.provides_salon_service')); ?></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                        
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="comission"><?php echo e(__('admin.platform_commission')); ?></label>
                                                <div class="controls">
                                                    <input type="number" step="0.01" name="comission" value="<?php echo e($salonCommission); ?>" class="form-control <?php $__errorArgs = ['comission'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="<?php echo e(__('admin.comission')); ?>" >
                                                    <?php $__errorArgs = ['home_fees'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Salon Images Tab -->

                            <div class="tab-pane" id="salon-images" role="tabpanel" aria-labelledby="salon-images-tab">
                                <div class="form-body mt-3">
                                    <div class="row">
                                      <div class="col-md-12 col-12">
                                        <div class="col-md-4 col-12">
                                            <div class="form-group">
                                                <label for="salon_images"><?php echo e(__('admin.past_work')); ?></label>
                                                <div class="controls">
                                                    <input type="file" accept="image/*" name="salon_images[]" class="form-control <?php $__errorArgs = ['salon_images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple>
                                                    <?php $__errorArgs = ['salon_images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                      </div>
                                    </div>
                                </div>
                            </div>

                                <!-- Documents Tab -->
                            <div class="tab-pane" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                                <div class="form-body mt-3">
                                    <div class="row">
                                        <div class="col-md-4 col-12">
                                            <div class="form-group">
                                                <label for="logo"><?php echo e(__('admin.logo')); ?> <span class="text-danger">*</span></label>
                                                <div class="controls">
                                                    <input type="file" accept="image/*" name="logo" class="form-control <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                    <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-12">
                                            <div class="form-group">
                                                <label for="commercial_register_image">
                                                    <span class="non-freelancer-label"><?php echo e(__('admin.commercial_register_image')); ?></span>
                                                    <span class="freelancer-label" style="display: none;"><?php echo e(__('admin.portfolio')); ?></span>
                                                    <span class="text-danger">*</span>
                                                </label>
                                                <div class="controls">
                                                    <input type="file" accept="image/*" name="commercial_register_image" class="form-control <?php $__errorArgs = ['commercial_register_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                    <?php $__errorArgs = ['commercial_register_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- ID Image for Saudi Freelancers -->
                                        <div class="col-md-4 col-12 freelancer-fields" style="display: none;">
                                            <div class="form-group">
                                                <label for="id_image"><?php echo e(__('admin.id_image')); ?> <span class="text-danger freelancer-required">*</span></label>
                                                <div class="controls">
                                                    <input type="file" accept="image/*" name="id_image" class="form-control <?php $__errorArgs = ['id_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                    <?php $__errorArgs = ['id_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-12">
                                            <div class="form-group">
                                                <label for="residence_image"><?php echo e(__('admin.residence_image')); ?> <small>(<?php echo e(__('admin.required_if_nationality_other')); ?>)</small></label>
                                                <div class="controls">
                                                    <input type="file" accept="image/*" name="residence_image" class="form-control <?php $__errorArgs = ['residence_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                                    <?php $__errorArgs = ['residence_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    </div>
                            </div>

                            <!-- Working Hours Tab -->
                            <div class="tab-pane" id="working-hours" role="tabpanel" aria-labelledby="working-hours-tab">
                                <div class="form-body mt-3">
                                    <div class="row">
                                        <div class="col-12">
                                            <h6 class="mb-3"><?php echo e(__('admin.working_hours')); ?></h6>
                                            <div class="working-hours-container">
                                                <?php
                                                    $days = [
                                                        'sunday' => __('admin.sunday'),
                                                        'monday' => __('admin.monday'),
                                                        'tuesday' => __('admin.tuesday'),
                                                        'wednesday' => __('admin.wednesday'),
                                                        'thursday' => __('admin.thursday'),
                                                        'friday' => __('admin.friday'),
                                                        'saturday' => __('admin.saturday')
                                                    ];
                                                ?>

                                                <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dayKey => $dayName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="card mb-2">
                                                    <div class="card-header bg-light-primary">
                                                        <h6 class="mb-0"><?php echo e($dayName); ?></h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row align-items-center">
                                                            <div class="col-md-3">
                                                                <div class="form-group mb-0">
                                                                    <div class="custom-control custom-switch">
                                                                        <input type="checkbox" class="custom-control-input"
                                                                               id="is_working_<?php echo e($dayKey); ?>"
                                                                               name="working_hours[<?php echo e($dayKey); ?>][is_working]"
                                                                               value="1"
                                                                               <?php echo e(old("working_hours.{$dayKey}.is_working", false) ? 'checked' : ''); ?>>
                                                                        <label class="custom-control-label" for="is_working_<?php echo e($dayKey); ?>">
                                                                            <?php echo e(__('admin.is_working')); ?>

                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group mb-0">
                                                                    <label for="start_time_<?php echo e($dayKey); ?>"><?php echo e(__('admin.start_time')); ?></label>
                                                                    <input type="time"
                                                                           class="form-control"
                                                                           id="start_time_<?php echo e($dayKey); ?>"
                                                                           name="working_hours[<?php echo e($dayKey); ?>][start_time]"
                                                                           value="<?php echo e(old("working_hours.{$dayKey}.start_time", '09:00')); ?>"
                                                                           disabled>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <div class="form-group mb-0">
                                                                    <label for="end_time_<?php echo e($dayKey); ?>"><?php echo e(__('admin.end_time')); ?></label>
                                                                    <input type="time"
                                                                           class="form-control"
                                                                           id="end_time_<?php echo e($dayKey); ?>"
                                                                           name="working_hours[<?php echo e($dayKey); ?>][end_time]"
                                                                           value="<?php echo e(old("working_hours.{$dayKey}.end_time", '22:00')); ?>"
                                                                           disabled>
                                                                </div>
                                                            </div>
                                                            <input type="hidden" name="working_hours[<?php echo e($dayKey); ?>][day]" value="<?php echo e($dayKey); ?>">
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 d-flex justify-content-center mt-3">
                            <button type="submit" class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.add')); ?></button>
                            <a href="<?php echo e(url()->previous()); ?>" type="reset" class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/toastr.min.js')); ?>"></script>

    
    <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
    <?php echo $__env->make('admin.shared.submitAddForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <?php echo $__env->make('admin.shared.regionCityDropdown', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <script>
        $(document).ready(function() {
            // Combined handler for salon type and nationality changes
            function handleFormFieldsVisibility() {
                var salonType = $('#salon_type').val();
                var nationality = $('select[name="nationality"]').val();

                // Handle salon type specific logic
                if (salonType === 'freelancer') {
                    // Hide non-freelancer fields
                    $('.non-freelancer-fields').hide();
                    $('.non-freelancer-label').hide();

                    // Show freelancer labels
                    $('.freelancer-label').show();

                    // Force nationality to be saudi for freelancers
                    if (nationality !== 'saudi') {
                        $('select[name="nationality"]').val('saudi').trigger('change');
                        nationality = 'saudi';
                    }
                    $('select[name="nationality"]').prop('disabled', true);

                    // Commercial register number not needed for freelancers
                } else {
                    // Show non-freelancer fields
                    $('.non-freelancer-fields').show();
                    $('.non-freelancer-label').show();

                    // Hide freelancer labels
                    $('.freelancer-label').hide();

                    // Enable nationality selection
                    $('select[name="nationality"]').prop('disabled', false);

                    // Commercial register number needed for non-freelancers
                }

                // Handle nationality specific logic
                if (nationality === 'saudi') {
                    // Hide residence fields for Saudi nationals
                    $('select[name="residence_type"]').closest('.form-group').parent().hide();
                    $('input[name="sponsor_name"]').closest('.form-group').parent().hide();
                    $('input[name="sponsor_phone"]').closest('.form-group').parent().hide();
                    $('input[name="residence_image"]').closest('.form-group').parent().hide();

                    // Residence fields not needed for Saudi nationals

                    // Show ID fields for Saudi freelancers
                    if (salonType === 'freelancer') {
                        $('.freelancer-fields').show();
                        $('.freelancer-required').show();
                        // ID fields shown for Saudi freelancers
                    } else {
                        $('.freelancer-fields').hide();
                        $('.freelancer-required').hide();
                        // ID fields hidden for non-freelancers
                    }
                } else if (nationality === 'other') {
                    // Show residence fields for non-Saudi nationals
                    $('select[name="residence_type"]').closest('.form-group').parent().show();
                    $('input[name="sponsor_name"]').closest('.form-group').parent().show();
                    $('input[name="sponsor_phone"]').closest('.form-group').parent().show();
                    $('input[name="residence_image"]').closest('.form-group').parent().show();

                    // Residence fields needed for non-Saudi nationals

                    // Hide ID fields (freelancers must be Saudi)
                    $('.freelancer-fields').hide();
                    $('.freelancer-required').hide();
                    // ID fields hidden for non-Saudi nationals
                } else {
                    // No nationality selected - hide all conditional fields
                    $('select[name="residence_type"]').closest('.form-group').parent().hide();
                    $('input[name="sponsor_name"]').closest('.form-group').parent().hide();
                    $('input[name="sponsor_phone"]').closest('.form-group').parent().hide();
                    $('input[name="residence_image"]').closest('.form-group').parent().hide();
                    $('.freelancer-fields').hide();
                    $('.freelancer-required').hide();

                    // No nationality selected - hide all conditional fields
                }
            }

            // Handle salon type and nationality changes
            $('#salon_type').on('change', handleFormFieldsVisibility);
            $('select[name="nationality"]').on('change', handleFormFieldsVisibility);

            // Initialize on page load
            handleFormFieldsVisibility();

            // Working hours toggle functionality
            $(document).on('change', '.custom-control-input[id^="is_working_"]', function() {
                var dayContainer = $(this).closest('.card-body');
                var startTimeInput = dayContainer.find('input[name*="[start_time]"]');
                var endTimeInput = dayContainer.find('input[name*="[end_time]"]');

                if ($(this).is(':checked')) {
                    startTimeInput.prop('disabled', false);
                    endTimeInput.prop('disabled', false);

                    // Set default values if inputs are empty
                    if (!startTimeInput.val()) {
                        startTimeInput.val('09:00');
                    }
                    if (!endTimeInput.val()) {
                        endTimeInput.val('22:00');
                    }
                } else {
                    startTimeInput.prop('disabled', true);
                    endTimeInput.prop('disabled', true);
                }
            });

            // Prevent double form submission
            $('.store').on('submit', function() {
                $(this).find('button[type="submit"]').prop('disabled', true);
            });

            // Enhanced tab error handling
            function initializeTabErrorHandling() {
                // Add click handlers for tabs with errors
                $('.nav-link.has-errors').on('click', function() {
                    // Remove the pulsing animation once clicked
                    $(this).find('.tab-error-indicator').removeClass('tab-error-indicator');
                });

                // Show toast notification if there are errors on form submission
                <?php if($errors->any()): ?>
                    toastr.error('<?php echo e(__("admin.please_fix_validation_errors_before_submitting")); ?>', '<?php echo e(__("admin.validation_errors")); ?>', {
                        timeOut: 5000,
                        closeButton: true,
                        progressBar: true
                    });
                <?php endif; ?>

                // Allow form submission to backend for validation
                $('.store').on('submit', function(e) {
                    // Just disable the submit button to prevent double submission
                    $(this).find('button[type="submit"]').prop('disabled', true).text('<?php echo e(__("admin.submitting")); ?>...');
                });
            }

            // Initialize tab error handling
            initializeTabErrorHandling();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\sorriso-backend\resources\views/admin/providers/create.blade.php ENDPATH**/ ?>